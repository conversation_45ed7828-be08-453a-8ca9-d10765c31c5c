import os
import re
import uuid
from typing import Dict, List, Any, Optional, Union
import pandas as pd
from preprocess.utils import (
    read_json, read_jsonl, read_parquet, save_jsonl, create_standard_format,
    extract_boxed_content, extract_tagged_content
)

def process_dianjin_r1(input_file: str, output_file: str) -> None:
    """处理dianjin-r1目录下的数据集

    字段：instruction，output
    - instruction作为user的content
    - system的content默认为空
    - 从output中提取<think>...</think>为reasoning
    - 从output中提取<answer>...</answer>为assistant的content
    - 从answer中匹配\boxed{xxx}，获取其中的xxx作为final_answer
    """
    data = read_json(input_file)
    processed_data = []

    for item in data:
        instruction = item.get("instruction", "")
        output = item.get("output", "")

        # 检测语言
        content_to_check = instruction or output
        is_chinese = any('\u4e00' <= char <= '\u9fff' for char in content_to_check)

        # 根据语言设置meta字段
        if is_chinese:
            language = "zh"
            # 根据文件名确定具体任务描述
            if "cflue_mcq" in input_file:
                task_desc = "金融领域多项选择题"
            elif "cflue_oe" in input_file:
                task_desc = "金融领域开放式问答"
            elif "fin_qa" in input_file:
                task_desc = "金融领域问答"
            else:
                task_desc = "金融领域问答"
        else:
            language = "en"
            if "cflue_mcq" in input_file:
                task_desc = "Financial multiple choice question answering"
            elif "cflue_oe" in input_file:
                task_desc = "Financial open-ended question answering"
            elif "fin_qa" in input_file:
                task_desc = "Financial question answering"
            else:
                task_desc = "Financial question answering"

        # 提取reasoning和answer
        reasoning = extract_tagged_content(output, "think") or ""
        answer_content = extract_tagged_content(output, "answer") or ""

        # 提取final_answer
        final_answer = extract_boxed_content(answer_content) or ""

        # 创建标准格式
        standard_item = create_standard_format(
            user_content=instruction,
            assistant_content=answer_content,
            reasoning=reasoning,
            final_answer=final_answer,
            language=language,
            task_desc=task_desc
        )

        processed_data.append(standard_item)

    save_jsonl(processed_data, output_file)

def process_finance_instruct_500k(input_file: str, output_file: str) -> None:
    """处理fin-r1/Finance-Instruct-500k目录下的数据集

    字段：system，user，assistant
    - 分别放到标准格式conversations中对应的content
    - 没有的字段都默认为空字符串
    - 根据内容语言动态设置meta字段
    """
    data = read_jsonl(input_file)
    processed_data = []

    for item in data:
        system_content = item.get("system", "")
        user_content = item.get("user", "")
        assistant_content = item.get("assistant", "")

        # 检测语言
        content_to_check = user_content or assistant_content
        is_chinese = any('\u4e00' <= char <= '\u9fff' for char in content_to_check)

        # 根据语言设置meta字段
        if is_chinese:
            language = "zh"
            task_desc = "金融领域通用指令问答"
        else:
            language = "en"
            task_desc = "Financial instruction question answering"

        # 创建标准格式
        standard_item = create_standard_format(
            system_content=system_content,
            user_content=user_content,
            assistant_content=assistant_content,
            language=language,
            task_desc=task_desc
        )

        processed_data.append(standard_item)

    save_jsonl(processed_data, output_file)

def process_financeiq(input_file: str, output_file: str) -> None:
    """处理fin-r1/FinanceIQ目录下的数据集

    字段：Question，A, B, C, D, E, Answer
    - 将Question和选项拼接为一个指令，放到user的content中
    - 拼接 "正确答案对应选项的描述\n\boxed{正确答案选项}" 为assistant的content
    - reasoning为空字符串
    - final_answer为正确答案选项字母
    """
    data = read_jsonl(input_file)
    processed_data = []

    system_content = "请将正确选项用\\boxed{{}}包裹。"

    # 确定数据集的语言和任务描述
    language = "zh"
    task_desc = "金融领域知识选择题"
    task_type = "mcq"

    for item in data:
        question = item.get("Question", "")
        answer_key = item.get("Answer", "")

        # 收集所有选项
        options = {}
        for key in ["A", "B", "C", "D", "E"]:
            if key in item and item[key]:
                options[key] = item[key]

        # 构建用户问题
        user_content = f"{question}\n"
        for key, value in options.items():
            user_content += f"{key}. {value}\n"

        # 构建助手回答
        if answer_key in options:
            answer_content = f"{options[answer_key]}\n正确答案是：\\boxed{{{answer_key}}}"
            final_answer = answer_key
        else:
            answer_content = "无法确定答案"
            final_answer = ""

        # 创建标准格式
        standard_item = create_standard_format(
            system_content=system_content,
            user_content=user_content,
            assistant_content=answer_content,
            final_answer=final_answer,
            language=language,
            task_desc=task_desc,
            task_type=task_type
        )

        processed_data.append(standard_item)

    save_jsonl(processed_data, output_file)

def process_fincuge_instruction(input_file: str, output_file: str) -> None:
    """处理fin-r1/FinCUGE-Instruction目录下的数据集

    FINNA (28800条): 新闻文本摘要任务
    FINCQA (21965条): 因果事件抽取任务
    FINQA (19906条): 事件抽取任务
    FINFE (16157条): 论坛情绪分析任务
    FINRE (13486条): 事件关系抽取任务
    FINESE (11752条): 事件主体抽取任务
    FINNL (7071条): 新闻分类任务
    FINNSP (4000条): 负面消息识别及主体判定任务

    字段：task, desc, instruction, input, output
    - 根据不同的task类型，采用不同的处理逻辑
    - 将特定的system_content提示添加到每种任务类型
    - 将input作为user的content
    - 根据任务类型从output中提取最终答案
    - 对于有明确选项或值的任务，使用\\boxed{}格式包裹最终答案
    - 对于没有明确选项或值的任务，直接使用output作为assistant_content
    - 确保final_answer是一个正确的唯一的明确的答案值
    """
    data = read_jsonl(input_file)
    processed_data = []
    skipped_count = 0

    for item in data:
        task = item.get("task", "")
        desc = item.get("desc", "")
        instruction = item.get("instruction", "")
        input_text = item.get("input", "")
        output = item.get("output", "")

        # 确定数据集的语言和任务描述
        language = "zh"
        task_desc = f"金融领域{desc}"

        # 初始化变量
        system_content = ""
        user_content = ""
        assistant_content = ""
        final_answer = ""
        task_type = ""

        # 根据任务类型处理数据
        if task == "FINFE":  # 论坛情绪分析任务 - 有明确选项
            # 构建system content，明确指定答案范围
            system_content = f"这是一个{desc}，请从'积极'、'消极'、'中性'三个选项中选择一个，并用\\boxed{{}}格式包裹最终答案。"
            user_content = f"{instruction}\n```{input_text}```"

            # 提取情感标签
            sentiment = None
            if "积极" in output:
                sentiment = "积极"
            elif "消极" in output:
                sentiment = "消极"
            elif "中性" in output:
                sentiment = "中性"

            # 构建assistant content和final_answer
            if sentiment:
                assistant_content = output.replace(sentiment, f"\\boxed{{{sentiment}}}")
                final_answer = sentiment
            else:
                # 如果没有找到明确的情感标签，跳过该样本
                skipped_count += 1
                continue

        elif task == "FINQA":  # 事件抽取任务 - 可能有明确值
            # 检查是否为事件抽取任务且output是从instruction中抽取的答案
            if '事件抽取任务' in desc:
                # 检查output是否在instruction中完整出现，且不包含否定词汇
                negative_words = ['无法', '不能', '暂无', '暂不', '更多']

                # 判断output是否是从instruction中抽取的答案
                is_extracted_answer = (
                    output in instruction and
                    not any(word in output for word in negative_words)
                )

                if is_extracted_answer:
                    # output是抽取的答案，设置为标准答案格式
                    system_content = f"这是一个{desc}，请仔细阅读文档内容，准确抽取所需信息，并将最终答案用\\boxed{{}}包裹。"
                    user_content = instruction
                    assistant_content = f"根据提供的文档内容，答案是：\\boxed{{{output}}}"
                    final_answer = output
                else:
                    # output不是抽取的答案，使用原有逻辑
                    system_content = f"这是一个{desc}"
                    user_content = instruction
                    assistant_content = output
            else:
                # 非事件抽取任务，使用原有逻辑
                system_content = f"这是一个{desc}"
                user_content = instruction
                assistant_content = output

        elif task == "FINCQA":  # 因果事件抽取任务 - 可能有明确值
            system_content = f"这是一个{desc}"
            user_content = instruction
            assistant_content = output

        elif task == "FINRE":  # 事件关系抽取任务 - 可能有明确值
            system_content = f"这是一个{desc}"
            user_content = f"{instruction}\n```{input_text}```"
            assistant_content = output

        elif task == "FINESE":  # 事件主体抽取任务 - 可能有明确值
            system_content = f"这是一个{desc}，请用\\boxed{{}}格式包裹最终抽取的主体。"
            user_content = f"{instruction}\n```{input_text}```"

            # 尝试从output中提取主体
            # 设计更灵活完备的正则表达式匹配各种主体提取模式
            entity_patterns = [
                # 常见的"主体是"模式及其变体
                r'主体是[：:]*\s*([^。；，\n]+)',
                r'主体[：:]\s*([^。；，\n]+)',
                r'金融主体是\s*([^。；，\n]+)',
                r'([^。；，\n]+)(?:就是|为|是)(?:该|此|这个|本次)?(?:事件|案例)的主体',
                # 特定事件类型的主体
                r'(?:负面|高管负面|交易违规|信批违规|重组失败|评级调整|业绩下滑|涉嫌非法集资|涉嫌欺诈|不能履职|实控人股东变更|资金账户风险)事件(?:的|相关的)?(?:主体|金融主体|相关主体)(?:是|为)?\s*[：:]*\s*([^。；，\n]+)',
                r'(?:所属|和|与)(?:.*?)(?:事件|风险)(?:的|相关的)?(?:主体|金融主体|相关主体)是\s*([^。；，\n]+)',
                # 描述性表达
                r'(?:事件|风险)描述的(?:主体|金融主体|相关主体)是\s*([^。；，\n]+)',
                # 直接给出主体
                r'(?:主体|金融主体|相关主体)[是为:：]\s*([^。；，\n]+)'
            ]

            # 尝试所有模式匹配
            entity_match = None
            for pattern in entity_patterns:
                entity_match = re.search(pattern, output)
                if entity_match:
                    break

            if entity_match:
                final_answer = entity_match.group(1).strip()
                # 清理可能的额外标点符号
                final_answer = re.sub(r'[。；，：:、]$', '', final_answer)
                assistant_content = output.replace(final_answer, f"\\boxed{{{final_answer}}}")
            else:
                # 如果没有找到最终答案，跳过该样本
                skipped_count += 1
                continue

        elif task == "FINNA":  # 新闻文本摘要任务 - 没有明确选项，但有明确值
            # 构建system content
            system_content = f"这是一个{desc}"
            user_content = f"{instruction}\n新闻内容：\n```{input_text}```"
            assistant_content = output

        elif task == "FINNL":  # 新闻分类任务 - 有明确选项
            # 构建system content
            system_content = f"这是一个{desc}，分类最终结果请用\\boxed{{}}格式包裹。"
            user_content = f"{instruction}\n新闻内容：\n```{input_text}```"

            # 定义可能的类别列表，用于直接匹配
            valid_categories = [
                '公司', '行业', '大盘', '中国', '国际', '经济', '政策',
                '期货', '债券', '房地产', '外汇', '虚拟货币', '新冠', '能源', '政治'
            ]

            # 设计更灵活完备的正则表达式匹配各种类别提取模式
            category_patterns = [
                # 常见的类别表述方式
                r'类别是\s*([^。；，\n]+)',
                r'归属于\s*([^。；，\n]+)(?:类别)?',
                r'属于\s*([^。；，\n]+)(?:类别)?',
                r'新闻(?:的)?类别(?:是|可能是)?\s*([^。；，\n]+)',
                r'(?:该|此|这个|以上)新闻(?:的)?类别是\s*([^。；，\n]+)',
                r'(?:该|此|这个|以上)新闻属于\s*([^。；，\n]+)(?:类别)?',
                r'(?:我们)?(?:认为|把|将)?(?:该|此|这个|以上)新闻归(?:类|属)(?:为|于)\s*([^。；，\n]+)(?:类别)?',
                r'(?:我们)?把以上新闻归属于\s*([^。；，\n]+)(?:类别)?',
                r'分析(?:该|此|以上)新闻，(?:我们)?认为(?:它|该新闻)属于\s*([^。；，\n]+)(?:类别)?'
            ]

            # 尝试所有模式匹配
            category_match = None
            for pattern in category_patterns:
                category_match = re.search(pattern, output)
                if category_match:
                    break

            # 如果没有匹配到，尝试直接在输出中查找有效类别
            if category_match:
                final_answer = category_match.group(1).strip()
                # 清理可能的额外标点符号和修饰词
                final_answer = re.sub(r'[。；，：:、]$', '', final_answer)
                final_answer = re.sub(r'^(是|为|属于|归属于|归类为)', '', final_answer)

                # 确保提取的类别在有效类别列表中
                found = False
                for category in valid_categories:
                    if category in final_answer:
                        final_answer = category
                        found = True
                        break

                if not found:
                    skipped_count += 1
                    continue

                # 构建assistant content
                assistant_content = f"根据新闻内容分析，该新闻属于\\boxed{{{final_answer}}}"
            else:
                # 如果没有找到最终答案，跳过该样本
                skipped_count += 1
                continue

        elif task == "FINNSP":  # 负面消息识别及主体判定任务 - 可能有明确值
            # 构建system content
            system_content = f"这是一个{desc}，请识别文本中的负面金融主体，如果存在则提取作为最终答案，并用\\boxed{{}}格式包裹，如果不存在则回答'无负面主体'。"
            user_content = f"{instruction}\n\n```{input_text}```"

            # 检查是否存在负面主体
            no_entity_patterns = [
                r'不存在负面主体',
                r'无负面主体',
                r'没有负面(?:金融)?(?:主体|实体)',
                r'不包含负面(?:金融)?(?:主体|实体)',
                r'(?:上述|该|此|以上)文本(?:中)?不存在负面(?:金融)?(?:主体|实体)',
                r'(?:上述|该|此|以上)文本(?:中)?没有负面(?:金融)?(?:主体|实体)',
                r'(?:分析)?(?:上述|该|此|以上)文本，(?:发现)?没有负面(?:金融)?(?:主体|实体)'
            ]

            is_no_entity = False
            for pattern in no_entity_patterns:
                if re.search(pattern, output):
                    is_no_entity = True
                    break

            if is_no_entity:
                final_answer = ""
                assistant_content = output
            else:
                # 设计更灵活完备的正则表达式匹配各种负面主体提取模式
                entity_patterns = [
                    # 常见的负面主体表述方式
                    r'负面主体[：:]\s*([^。；，\n]+)',
                    r'负面金融实体[：:]\s*([^。；，\n]+)',
                    r'负面主体是\s*([^。；，\n]+)',
                    r'负面金融实体有(?:以下几个)?[：:]*\s*([^。；，\n]+)',
                    r'负面金融主体(?:包含|有)(?:以下几个)?[：:]*\s*([^。；，\n]+)',
                    r'文中包含的负面主体[：:]*\s*([^。；，\n]+)',
                    r'(?:上述|该|此|以上)文本中(?:的)?负面(?:金融)?(?:主体|实体)(?:是|为|包含|有)[：:]*\s*([^。；，\n]+)',
                    # 多个主体的情况
                    r'负面(?:金融)?(?:主体|实体)(?:有|包含)(?:以下几个)?[：:]*\s*([^。；，\n]+)'
                ]

                # 尝试所有模式匹配
                entity_match = None
                for pattern in entity_patterns:
                    entity_match = re.search(pattern, output)
                    if entity_match:
                        break

                if entity_match:
                    final_answer = entity_match.group(1).strip()

                    # 处理多个主体的情况（以分号分隔的列表）
                    if ';' in final_answer or '；' in final_answer:
                        # 将分号替换为逗号，保持一致的格式
                        final_answer = final_answer.replace(';', '，').replace('；', '，')

                    # 清理可能的额外标点符号
                    final_answer = re.sub(r'[。；，：:、]$', '', final_answer)

                    assistant_content = f"根据文本分析，负面主体是：\\boxed{{{final_answer}}}"
                else:
                    # 如果没有找到明确的模式，跳过该样本
                    skipped_count += 1
                    continue

        else:
            # 默认处理方式 - 跳过不明确的任务类型
            skipped_count += 1
            continue

        # 创建标准格式
        standard_item = create_standard_format(
            system_content=system_content,
            user_content=user_content,
            assistant_content=assistant_content,
            final_answer=final_answer,
            language=language,
            task_desc=task_desc
        )

        processed_data.append(standard_item)

    print(f"处理完成: 总样本数 {len(data)}, 处理样本数 {len(processed_data)}, 跳过样本数 {skipped_count}")
    save_jsonl(processed_data, output_file)

def process_quant_trading_instruct(input_file: str, output_file: str) -> None:
    """处理fin-r1/Quant-Trading-Instruct目录下的数据集

    字段：context, answer, question
    - 将context作为system的content
    - 将question作为user的content
    - 将answer作为assistant的content
    - reasoning和final_answer为空字符
    """
    data = read_jsonl(input_file)
    processed_data = []

    # 确定数据集的语言和任务描述
    language = "en"
    task_desc = "Quantitative trading instruction question answering"

    for item in data:
        context = item.get("context", "")
        answer = item.get("answer", "")
        question = item.get("question", "")

        # 创建标准格式
        standard_item = create_standard_format(
            system_content=context,
            user_content=question,
            assistant_content=answer,
            language=language,
            task_desc=task_desc
        )

        processed_data.append(standard_item)

    save_jsonl(processed_data, output_file)

def process_twitter_financial_news_sentiment(input_file: str, output_file: str) -> None:
    """处理fin-r1/twitter-financial-news-sentiment目录下的数据集

    字段：text, label
    - label为0，1，2之一，其中0代表Bearish, 1代表Bullish，2代表Neutral
    - 将"请对一下推文的进行Bearish，Bullish，Neutral的分类。"作为system的content
    - 将text作为user的content
    - 将label映射为Bearish、Bullish、Neutral，使用\boxed{正确答案}包裹作为assistant的content
    - reasoning为空字符
    - final_answer为\boxed{正确答案}中的正确答案，即Bearish、Bullish、Neutral之一
    """
    data = read_jsonl(input_file)
    processed_data = []

    # 标签映射
    label_map = {
        0: "Bearish",
        1: "Bullish",
        2: "Neutral"
    }

    system_content = "Please classify the following tweets into Bearish, Bullish, and Neutral. Please use \\boxed{{}} to wrap your final answer."

    # 确定数据集的语言和任务描述
    language = "en"
    task_desc = "Financial news sentiment classification"

    for item in data:
        text = item.get("text", "")
        label = item.get("label")

        # 获取标签文本
        sentiment = label_map.get(label, "")

        # 构建assistant content
        assistant_content = f"\\boxed{{{sentiment}}}"

        # 创建标准格式
        standard_item = create_standard_format(
            system_content=system_content,
            user_content=text,
            assistant_content=assistant_content,
            final_answer=sentiment,
            language=language,
            task_desc=task_desc
        )

        processed_data.append(standard_item)

    save_jsonl(processed_data, output_file)

def process_finqa(input_file: str, output_file: str) -> None:
    """处理fin-r1/finqa目录下的数据集

    字段：id, post_text, pre_text, question, answer, gold_evidence, table
    - 将pre_text和post_text合并作为背景信息，放入system的content
    - 将question作为user的content
    - 将answer作为final_answer
    - 将gold_evidence作为reasoning
    - 构建assistant的content为answer
    """
    data = read_jsonl(input_file)
    processed_data = []

    # 确定数据集的语言和任务描述
    language = "en"
    task_desc = "Financial numerical reasoning with tables and text"

    for item in data:
        # 获取字段
        pre_text = " ".join(item.get("pre_text", []))
        post_text = " ".join(item.get("post_text", []))
        question = item.get("question", "")
        answer = item.get("answer", "")
        gold_evidence = " ".join(item.get("gold_evidence", []))

        # 构建system content (背景信息)
        system_content = ""
        if pre_text:
            system_content += f"previous text: {pre_text}\n\n"
        if post_text:
            system_content += f"post text: {post_text}\n\n"
        if "table" in item and item["table"]:
            system_content += "table:\n"
            for row in item["table"]:
                system_content += " | ".join([str(cell) for cell in row]) + "\n"

        # 构建assistant content
        assistant_content = f"The answer is: {answer}"

        # 创建标准格式
        standard_item = create_standard_format(
            system_content=system_content,
            user_content=question,
            assistant_content=assistant_content,
            reasoning=gold_evidence,
            final_answer=answer,
            language=language,
            task_desc=task_desc
        )

        processed_data.append(standard_item)

    save_jsonl(processed_data, output_file)


def process_fincorpus(input_file: str, output_file: str) -> None:
    """处理fin-r1/FinCorpus目录下的数据集

    字段：text, meta
    - system_content：添加统一的金融考试助手角色提示
    - user_content：从text中提取'\n答案：'之前的部分
    - assistant_content：根据是否有分析解释构建回答，包含'\boxed{答案选项}'格式
    - final_answer：从答案部分提取的ABCDEFGH选项字母

    处理逻辑：
    1. 跳过不包含'\n答案：'的样本
    2. 跳过答案部分包含"你的答案"的样本
    3. 使用正则表达式从答案部分提取ABCDEFGH选项字母
    4. 如果有分析解释，拼接解释和答案；否则只提供答案
    """
    data = read_jsonl(input_file)
    processed_data = []
    skipped_count = 0

    # 定义统一的system_content
    system_content = "你是一个专业的金融考试助手，擅长解答金融、会计、税务、经济等领域的考试题目。请仔细分析题目，给出清晰的解题思路和正确答案。对于选择题，请在回答的最后用\\boxed{X}标注最终答案，其中X为选项字母。"

    # 确定数据集的语言和任务描述
    language = "zh"
    task_desc = "金融领域考试选择题"
    task_type = "mcq"

    for item in data:
        text = item.get("text", "")

        # 检查是否包含'\n答案：'
        if '\n答案：' not in text:
            skipped_count += 1
            continue

        # 分割文本获取user_content
        parts = text.split('\n答案：')
        if len(parts) != 2:
            skipped_count += 1
            continue

        user_content = parts[0]

        # 分割获取答案和分析解释
        answer_parts = parts[1].split('\n分析解释：')
        if len(answer_parts) < 1:
            skipped_count += 1
            continue

        answer_text = answer_parts[0].strip()
        explanation = answer_parts[1].strip() if len(answer_parts) > 1 else ""

        # 如果答案部分包含"你的答案"，则跳过
        if "你的答案" in answer_text:
            skipped_count += 1
            continue

        # 使用正则表达式提取答案中的选项字母
        option_matches = re.findall(r'[A-H]', answer_text)
        if not option_matches:
            skipped_count += 1
            continue

        # 去重并按字母顺序排序
        answer_options = sorted(set(option_matches))
        answer = "".join(answer_options)

        # 构建assistant_content
        if explanation:
            assistant_content = f"{explanation}\n\n因此，最终答案是\\boxed{{{answer}}}"
        else:
            assistant_content = f"正确答案是\\boxed{{{answer}}}"

        # 创建标准格式，添加system_content
        standard_item = create_standard_format(
            system_content=system_content,
            user_content=user_content,
            assistant_content=assistant_content,
            final_answer=answer,
            language=language,
            task_desc=task_desc,
            task_type=task_type
        )

        processed_data.append(standard_item)

    print(f"处理完成: 总样本数 {len(data)}, 处理样本数 {len(processed_data)}, 跳过样本数 {skipped_count}")
    save_jsonl(processed_data, output_file)


def process_convfinqa(input_file: str, output_file: str) -> None:
    """处理fin-r1/fingpt-convfinqa目录下的数据集

    字段：system_content, user_content, assistant_content, final_answer, documents
    - system_content：使用原始数据中的instruction
    - documents：提取第一个"Question: "之前的文本作为文档
    - 多轮对话：将成对出现的Question和Answer转为user_content和assistant_content
    - 最后一轮：最后的Question作为user_content，output作为final_answer
    - assistant_content：拼接"The answer is: " + \boxed{output}

    处理逻辑：
    1. 提取第一个"Question: "之前的内容作为文档，存入documents字段
    2. 解析多轮对话，将前面的Question-Answer对转为user-assistant对话
    3. 最后一个Question作为最后一轮的user_content
    4. 将output作为最后一轮的final_answer
    """
    data = read_jsonl(input_file)
    processed_data = []
    skipped_count = 0

    # 确定数据集的语言和任务描述
    language = "en"
    task_desc = "Financial conversational question answering"

    for item in data:
        # 获取字段
        input_text = item.get("input", "")
        output = item.get("output", "")
        instruction = item.get("instruction", "")

        # 检查必要字段是否存在
        if not input_text or not output:
            skipped_count += 1
            continue

        # 提取文档和问答部分
        if "Question: " in input_text:
            parts = input_text.split("Question: ", 1)
            document = parts[0].strip()
            qa_text = "Question: " + parts[1].strip()
        else:
            document = ""
            qa_text = input_text

        # 解析多轮对话
        conversations = []

        # 添加system消息
        conversations.append({
            "role": "system",
            "content": instruction
        })

        # 处理多轮问答
        qa_parts = qa_text.split("Question: ")
        qa_parts = [p for p in qa_parts if p.strip()]  # 移除空字符串

        # 处理除最后一轮外的所有问答对
        for i in range(len(qa_parts) - 1):
            part = qa_parts[i]
            if "Answer: " in part:
                q_a_parts = part.split("Answer: ", 1)
                question = q_a_parts[0].strip()
                answer = q_a_parts[1].strip()

                # 如果answer后面还有Question，截取到Question前
                if " Question: " in answer:
                    answer = answer.split(" Question: ", 1)[0].strip()

                # 添加用户问题
                conversations.append({
                    "role": "user",
                    "content": question
                })

                # 添加助手回答
                conversations.append({
                    "role": "assistant",
                    "content": f"\\boxed{{{answer}}}",
                    "reasoning": "",
                    "final_answer": answer
                })

        # 处理最后一轮问答
        last_question = qa_parts[-1].strip()
        if "Answer: " in last_question:
            # 如果最后一部分也包含Answer，则分开处理
            last_parts = last_question.split("Answer: ", 1)
            last_question = last_parts[0].strip()
            # 忽略这个答案，因为我们会使用output作为最终答案

        # 添加最后的用户问题
        conversations.append({
            "role": "user",
            "content": last_question
        })

        # 添加最后的助手回答
        conversations.append({
            "role": "assistant",
            "content": f"The answer is: \\boxed{{{output}}}",
            "reasoning": "",
            "final_answer": output
        })

        # 创建标准格式的item
        standard_item = {
            "id": str(uuid.uuid4()),
            "conversations": conversations,
            "documents": [document] if document else [],
            "meta": {
                "language": language,
                "task_desc": task_desc
            }
        }

        processed_data.append(standard_item)

    print(f"处理完成: 总样本数 {len(data)}, 处理样本数 {len(processed_data)}, 跳过样本数 {skipped_count}")
    save_jsonl(processed_data, output_file)
