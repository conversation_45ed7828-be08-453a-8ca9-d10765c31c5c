# 金融类数据合成方案

## 一、数据收集

### 1.1 数据集信息

<center>

表1-1 数据集信息统计

| 数据集名称 | 任务描述 | 任务类型 | 来源 | 数据量 |
|:----------|:--------|:-----|:-----|:-----:|
| dianjin-r1 | 金融领域多项选择题、开放式问答和一般问答任务。 | | hf: DianJin/DianJin-R1-Data<br>部分未开源 | 36,568 |
| Finance-Instruct-500k | 金融领域通用指令问答任务，涵盖各种金融话题和问题类型。 | | hf: Josephgflowers/Finance-Instruct-500k | 518,185 |
| FinCUGE | 金融领域多类型NLP任务集合，包括情感分析、文本摘要、事件抽取等多种任务类型。 | | hf: Maciel/FinCUGE-Instruction | 137,852 |
| FinCorpus | 金融考试题目问答任务，主要包含会计、税务、经济等领域的选择题。 | mcq | hf: Duxiaoman-DI/FinCorpus | 489,914 |
| Ant_Finance | 金融领域多选题问答任务，包含金融产品分析、金融情绪识别等多种类型。 | | https://github.com/alipay/financial_evaluation_dataset | 8,444 |
| ConvFinQA | 金融多轮对话问答任务，基于财报文本和表格数据进行连续多轮问答。 | | hf: FinGPT/fingpt-convfinqa | 12,594 |
| TFNS | 金融推文情感分析任务，将推文分类为看跌(Bearish)、看涨(Bullish)或中性(Neutral)。 | | hf: zeroshot/twitter-financial-news-sentiment | 11,931 |
| FinQA | 金融文档问答任务，基于财报文本和表格数据回答数值计算类问题。 | | hf: dreamerdeo/finqa | 8,281 |
| FinanceIQ | 金融领域多选题问答任务，测试金融知识和概念理解。 | mcq | hf: Duxiaoman-DI/FinanceIQ | 7,173 |
| Ant_Finance_SUFE | 金融领域专业考试题目，主要包含会计学、金融学等专业知识的选择题。 | | https://github.com/alipay/financial_evaluation_dataset | 4,581 |
| FinanceQT | 量化交易指令问答任务，涵盖算法交易、策略开发和量化分析等内容。 | | hf: lumalik/Quant-Trading-Instruct | 386 |
| FinPEE | - | | 未开源 | - |

</center>

### 1.2 配比参考

#### 1.2.1 Fin-R1配比参考

<center>

表1-2 Fin-R1蒸馏数据参考分布

| 数据集 | 数据量 |
| --- | ---: |
| ConvFinQA-R1-Distill | 7,629 |
| Finance-Instruct-500K-R1-Distill | 11,300 |
| FinCUGE-R1-Distill | 2,000 |
| FinQA-R1-Distill | 2,948 |
| TFNS-R1-Distill | 2,451 |
| FinanceIQ-R1-Distill | 2,596 |
| FinanceQT-R1-Distill | 152 |
| Ant_Finance-R1-Distill | 1,548 |
| FinCorpus-R1-Distill | 29,288 |
| FinPEE-R1-Distill | 179 |
| **总计** | **60,091** |

</center>

## 二、数据处理

### 2.1. 数据格式标准化

标准格式

```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "xxx"},
    ...(user, assistant可能的多轮对话)
    {"role": "user", "content": "xxx"},
    {"role": "assistant", "content": "xxx", "reasoning": "xxx", "final_answer": "xxx"}
  ]
}
```

#### 2.1.1. dianjin-r1

**任务描述**：金融领域多项选择题、开放式问答和一般问答任务。

**原始格式**：JSON文件，包含字段：`instruction`，`output`
- `instruction`：用户提问或指令
- `output`：助手回答，包含思考过程和最终答案

**标准化处理**：
- `system.content`：默认为空字符串
- `user.content`：直接使用原始数据的`instruction`字段
- `assistant.reasoning`：从`output`中提取`<think>...</think>`标签包裹的内容
- `assistant.content`：从`output`中提取`<answer>...</answer>`标签包裹的内容
- `assistant.final_answer`：从`assistant.content`中提取`\boxed{xxx}`格式的内容，如有多个取最后一个

**示例**：
原始数据：
```json
{
  "instruction": "假设你是一位金融行业专家，请回答下列问题...",
  "output": "<think>首先分析这个问题...</think><answer>经过分析，答案是\\boxed{D}</answer>"
}
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": ""},
    {"role": "user", "content": "假设你是一位金融行业专家，请回答下列问题..."},
    {"role": "assistant", "content": "经过分析，答案是\\boxed{D}", "reasoning": "首先分析这个问题...", "final_answer": "D"}
  ]
}
```

#### 2.1.2. fin-r1: Finance-Instruct-500k

**任务描述**：金融领域通用指令问答任务，涵盖各种金融话题和问题类型。

**原始格式**：JSONL文件，包含字段：`system`，`user`，`assistant`
- `system`：系统指令
- `user`：用户提问
- `assistant`：助手回答

**标准化处理**：
- `system.content`：直接使用原始数据的`system`字段
- `user.content`：直接使用原始数据的`user`字段
- `assistant.content`：直接使用原始数据的`assistant`字段
- `assistant.reasoning`：默认为空字符串
- `assistant.final_answer`：默认为空字符串

**示例**：
原始数据：
```json
{"system": "\n", "user": "Explain tradeoffs between fiscal and monetary policy...", "assistant": "Fiscal and monetary policy are the two main tools..."}
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "\n"},
    {"role": "user", "content": "Explain tradeoffs between fiscal and monetary policy..."},
    {"role": "assistant", "content": "Fiscal and monetary policy are the two main tools...", "reasoning": "", "final_answer": ""}
  ]
}
```

#### 2.1.3. fin-r1: FinanceIQ

**任务描述**：金融领域多选题问答任务，测试金融知识和概念理解。

**原始格式**：JSONL文件，包含字段：`Question`，`A`，`B`，`C`，`D`，`E`，`Answer`
- `Question`：问题内容
- `A`到`E`：选项内容
- `Answer`：正确答案选项字母

**标准化处理**：
- `system.content`：将"请将正确选项用\\boxed{{}}包裹。"作为`system.content`
- `user.content`：将`Question`和所有选项（`A`到`E`）拼接为一个完整的问题
- `assistant.content`：拼接"正确答案对应选项的描述\n`\boxed{正确答案选项}`"
- `assistant.reasoning`：默认为空字符串
- `assistant.final_answer`：使用正确答案选项字母（`Answer`字段的值）

**示例**：
原始数据：
```json
{"Question": "关于生命价值理论的理解，以下哪一项表述是不正确的？", "A": "补偿生命经济价值可能受到的损失...", "B": "个人预期收入的货币价值的现值可以体现为生命的经济价值", "C": "任何触及个人收入能力的事件都能够影响生命的经济价值", "D": "早逝、残疾、退休或失业可能导致生命的经济价值丧失", "Answer": "B"}
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": ""},
    {"role": "user", "content": "关于生命价值理论的理解，以下哪一项表述是不正确的？\nA. 补偿生命经济价值可能受到的损失...\nB. 个人预期收入的货币价值的现值可以体现为生命的经济价值\nC. 任何触及个人收入能力的事件都能够影响生命的经济价值\nD. 早逝、残疾、退休或失业可能导致生命的经济价值丧失\n"},
    {"role": "assistant", "content": "个人预期收入的货币价值的现值可以体现为生命的经济价值\n\\boxed{B}", "reasoning": "", "final_answer": "B"}
  ]
}
```

#### 2.1.4. fin-r1: FinCUGE-Instruction

**任务描述**：金融领域多类型NLP任务集合，包括情感分析、文本摘要、事件抽取等多种任务类型。

**原始格式**：JSONL文件，包含字段：`task`，`desc`，`instruction`，`input`，`output`
- `task`：任务类型标识（如FINFE、FINNL等）
- `desc`：任务描述（如"论坛情绪分析任务"）
- `instruction`：具体指令
- `input`：输入文本
- `output`：输出结果

**标准化处理**：
- 根据不同的任务类型采用不同的处理逻辑
- `system.content`：根据任务类型构建特定的系统提示
- `user.content`：结合`instruction`和`input`构建用户提问
- `assistant.content`：根据任务类型从`output`中提取或构建回答
- `final_answer`：对于有明确选项或值的任务，提取最终答案并用`\boxed{}`格式包裹

<center>

表2-1 FinCUGE-Instruction数据集任务类型及处理方式

| 任务标识 | 任务名称 | 数量 | 处理方式 |
|---------|---------|------|---------|
| FINNA | 新闻文本摘要任务 | 28800 | 直接使用原始输出作为回答内容；不提取特定的最终答案；构建系统提示说明这是一个新闻文本摘要任务 |
| FINCQA | 因果事件抽取任务 | 21965 | 直接使用原始输出作为回答内容；不提取特定的最终答案；构建系统提示说明这是一个因果事件抽取任务 |
| FINQA | 事件抽取任务 | 19906 | 直接使用原始输出作为回答内容；不提取特定的最终答案；构建系统提示说明这是一个事件抽取任务 |
| FINFE | 论坛情绪分析任务 | 16157 | 从输出中提取"积极"、"消极"或"中性"作为最终答案；使用正则表达式匹配情感标签；构建带有`\boxed{}`格式的最终答案 |
| FINRE | 事件关系抽取任务 | 13486 | 直接使用原始输出作为回答内容；不提取特定的最终答案；构建系统提示说明这是一个事件关系抽取任务 |
| FINESE | 事件主体抽取任务 | 11752 | 使用多种正则表达式模式匹配事件主体；清理提取的主体文本，去除多余标点符号；构建带有`\boxed{}`格式的最终答案 |
| FINNL | 新闻分类任务 | 7071 | 使用多种正则表达式模式匹配新闻类别；将提取的类别与预定义的有效类别列表（如"公司"、"行业"、"大盘"等）进行匹配；构建带有`\boxed{}`格式的最终答案 |
| FINNSP | 负面消息识别及主体判定任务 | 4000 | 首先判断是否存在负面主体；如果存在，使用正则表达式提取负面主体；处理多个主体的情况（以分隔符分隔的列表）；构建带有`\boxed{}`格式的最终答案 |

</center>

**示例（FINFE任务）**：
原始数据：
```json
{"task": "FINFE", "desc": "论坛情绪分析任务", "instruction": "这个文本的情感倾向是积极、消极还是中性的。", "input": "钼已成粥", "output": "中性。"}
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "这是一个论坛情绪分析任务，请从'积极'、'消极'、'中性'三个选项中选择一个，并用\\boxed{{}}格式包裹最终答案。"},
    {"role": "user", "content": "这个文本的情感倾向是积极、消极还是中性的。\n```钼已成粥```"},
    {"role": "assistant", "content": "中性。\\boxed{中性}", "reasoning": "", "final_answer": "中性"}
  ]
}
```

#### 2.1.5. fin-r1: Quant-Trading-Instruct

**任务描述**：量化交易指令问答任务，涵盖算法交易、策略开发和量化分析等内容。

**原始格式**：JSONL文件，包含字段：`context`，`answer`，`question`
- `context`：背景信息或系统提示
- `question`：用户问题
- `answer`：助手回答

**标准化处理**：
- `system.content`：直接使用原始数据的`context`字段
- `user.content`：直接使用原始数据的`question`字段
- `assistant.content`：直接使用原始数据的`answer`字段
- `assistant.reasoning`：默认为空字符串
- `assistant.final_answer`：默认为空字符串

**示例**：
原始数据：
```json
{"context": "You are a helpful assistant for quant trading.", "answer": "# QUANTCONNECT.COM - Democratizing Finance...", "question": "Please provide me with a basic template algorithm that trades futures and includes extended market hours."}
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "You are a helpful assistant for quant trading."},
    {"role": "user", "content": "Please provide me with a basic template algorithm that trades futures and includes extended market hours."},
    {"role": "assistant", "content": "# QUANTCONNECT.COM - Democratizing Finance...", "reasoning": "", "final_answer": ""}
  ]
}
```

#### 2.1.6. fin-r1: twitter-financial-news-sentiment

**任务描述**：金融推文情感分析任务，将推文分类为看跌(Bearish)、看涨(Bullish)或中性(Neutral)。

**原始格式**：JSONL文件，包含字段：`text`，`label`
- `text`：推文内容
- `label`：情感标签（0=Bearish，1=Bullish，2=Neutral）

**标准化处理**：
- `system.content`：固定为`"Please classify the following tweets into Bearish, Bullish, and Neutral. Please use \\boxed{{}} to wrap your final answer."`
- `user.content`：直接使用原始数据的`text`字段
- `assistant.content`：将`label`映射为对应的情感标签并包装为`\boxed{标签}`格式
- `assistant.reasoning`：默认为空字符串
- `assistant.final_answer`：映射后的情感标签（Bearish、Bullish或Neutral）

**标签映射**：
- 0 → Bearish（消极）
- 1 → Bullish（积极）
- 2 → Neutral（中性）

**示例**：
原始数据：
```json
{"text": "$ALLY - Ally Financial pulls outlook https://t.co/G9Zdi1boy5", "label": 0}
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "Please classify the following tweets into Bearish, Bullish, and Neutral. Please use \\boxed{{}} to wrap your final answer."},
    {"role": "user", "content": "$ALLY - Ally Financial pulls outlook https://t.co/G9Zdi1boy5"},
    {"role": "assistant", "content": "\\boxed{Bearish}", "reasoning": "", "final_answer": "Bearish"}
  ]
}
```

#### 2.1.7. fin-r1: finqa

**任务描述**：金融文档问答任务，基于财报文本和表格数据回答数值计算类问题。

**原始格式**：JSONL文件，包含字段：`id`，`post_text`，`pre_text`，`question`，`answer`，`gold_evidence`，`table`
- `pre_text`：前文信息（数组）
- `post_text`：后文信息（数组）
- `question`：问题
- `answer`：答案
- `gold_evidence`：支持答案的证据（数组）
- `table`：表格数据（二维数组）

**标准化处理**：
- `system.content`：合并`pre_text`、`post_text`和`table`作为背景信息
- `user.content`：直接使用原始数据的`question`字段
- `assistant.content`：格式化为`"The answer is: {answer}"`
- `assistant.reasoning`：合并`gold_evidence`数组中的所有元素
- `assistant.final_answer`：直接使用原始数据的`answer`字段

**示例**：
原始数据（简化）：
```json
{"id": "ADI/2009/page_49.pdf-1", "post_text": ["fair value of forward exchange contracts..."], "pre_text": ["interest rate to a variable interest rate..."], "question": "what is the the interest expense in 2009?", "answer": "380", "gold_evidence": ["if libor changes by 100 basis points , our annual interest expense would change by $ 3.8 million ."], "table": [...]}
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "previous text: interest rate to a variable interest rate...\n\npost text: fair value of forward exchange contracts...\n\ntable:\n..."},
    {"role": "user", "content": "what is the the interest expense in 2009?"},
    {"role": "assistant", "content": "The answer is: 380", "reasoning": "if libor changes by 100 basis points , our annual interest expense would change by $ 3.8 million .", "final_answer": "380"}
  ]
}
```

#### 2.1.8. fin-r1: FinCorpus

仅使用`fin_exam`部分

**任务描述**：金融考试题目问答任务，主要包含会计、税务、经济等领域的选择题。

**原始格式**：JSONL文件，包含字段：`text`, `meta`
- `text`：包含问题、答案和分析解释的文本
- `meta`：元数据信息

**标准化处理**：
- `system.content`：统一设置为金融考试助手角色提示
- `user.content`：从`text`中提取'\n答案：'之前的部分作为问题内容
- `assistant.content`：根据是否有分析解释构建回答，包含'\boxed{答案选项}'格式
- `assistant.reasoning`：默认为空字符串
- `assistant.final_answer`：从答案部分提取的ABCDEFGH选项字母

**处理逻辑**：
1. 跳过不包含'\n答案：'的样本
2. 跳过答案部分包含"你的答案"的样本
3. 使用正则表达式从答案部分提取ABCDEFGH选项字母
4. 如果有分析解释，拼接解释和答案；否则只提供答案

**示例**：
原始数据：
```json
{"text": "某酒厂为增值税一般纳税人，2019年7月销售白酒4000斤，取得销售收入13560元（含增值税）。已知白酒消费税定额税率为0.5元/斤，比例税率为20%。该酒厂7月应缴纳的消费税税额为（ ）元。\nA、2000\nB、2400\nC、4400\nD、5000\n答案：C\n分析解释：该酒厂7月应缴纳的消费税税额=（13560/1.13）\\*20%+4000\\*0.5=4400（元）。", "meta": {"id": "fin_exam_0"}}
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "你是一个专业的金融考试助手，擅长解答金融、会计、税务、经济等领域的考试题目。请仔细分析题目，给出清晰的解题思路和正确答案。对于选择题，请在回答的最后用\\boxed{X}标注最终答案，其中X为选项字母。"},
    {"role": "user", "content": "某酒厂为增值税一般纳税人，2019年7月销售白酒4000斤，取得销售收入13560元（含增值税）。已知白酒消费税定额税率为0.5元/斤，比例税率为20%。该酒厂7月应缴纳的消费税税额为（ ）元。\nA、2000\nB、2400\nC、4400\nD、5000"},
    {"role": "assistant", "content": "该酒厂7月应缴纳的消费税税额=（13560/1.13）\\*20%+4000\\*0.5=4400（元）。\n\n因此，最终答案是\\boxed{C}", "reasoning": "", "final_answer": "C"}
  ]
}
```

#### 2.1.9. fin-r1: fingpt-convfinqa

**任务描述**：金融多轮对话问答任务，基于财报文本和表格数据进行连续多轮问答。

**原始格式**：JSONL文件，包含字段：`input`，`output`，`instruction`
- `input`：包含文本、表格数据和问题，可能包含多轮问答
- `output`：最终问题的答案
- `instruction`：系统指令

**标准化处理**：
- `system.content`：直接使用原始数据的`instruction`字段
- `documents`：提取第一个"Question: "之前的文本作为文档
- `user.content`：提取问题部分，对于多轮对话提取每个问题
- `assistant.content`：对于多轮对话中的前几轮，使用对应的Answer；对于最后一轮，使用`output`字段
- `assistant.reasoning`：默认为空字符串
- `assistant.final_answer`：对于多轮对话中的前几轮，使用对应的Answer；对于最后一轮，使用`output`字段
- 所有assistant回答都使用`\boxed{}`格式包裹答案

**处理逻辑**：
1. 提取第一个"Question: "之前的内容作为文档，存入documents字段
2. 解析多轮对话，将前面的Question-Answer对转为user-assistant对话
3. 最后一个Question作为最后一轮的user_content
4. 将output作为最后一轮的final_answer

**示例**：
原始数据：
```json
{"input": "stock-based awards under the plan... <table>...</table>.\nQuestion: what was the weighted average exercise price per share in 2007?\nAnswer: 60.94\nQuestion: what about in 2008?", "output": "37.84", "instruction": "Read the following texts and table with financial data from an S&P 500 earnings report carefully.Based on the question-answer history (if provided), answer the last question. The answer may require mathematical calculation based on the data provided.\n"}
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "Read the following texts and table with financial data from an S&P 500 earnings report carefully.Based on the question-answer history (if provided), answer the last question. The answer may require mathematical calculation based on the data provided.\n"},
    {"role": "user", "content": "what was the weighted average exercise price per share in 2007?"},
    {"role": "assistant", "content": "The answer is: \\boxed{60.94}", "reasoning": "", "final_answer": "60.94"},
    {"role": "user", "content": "what about in 2008?"},
    {"role": "assistant", "content": "The answer is: \\boxed{37.84}", "reasoning": "", "final_answer": "37.84"}
  ],
  "documents": ["stock-based awards under the plan... <table>...</table>."]
}
```

#### 2.1.10. fin-r1: Ant_Finance

**任务描述**：金融领域多选题问答任务，包含金融产品分析、金融情绪识别等多种类型。

**原始格式**：CSV文件，包含字段：`id`，`question`，`A`，`B`，`C`，`D`，`E`（可选），`answer`，`context`（可选）
- `id`：问题ID
- `question`：问题内容
- `A`到`E`：选项内容
- `answer`：正确答案选项字母
- `context`：背景信息（部分问题有）

**标准化处理**：
- `system.content`：根据不同领域构建不同的角色描述，并添加要求将答案放在`\boxed{}`格式中
- `user.content`：根据不同领域构建不同的问题格式，包含问题和选项
- `assistant.content`：使用`\boxed{}`包裹的正确答案选项字母
- `assistant.reasoning`：默认为空字符串
- `assistant.final_answer`：使用`\boxed{}`包裹的正确答案选项字母

**处理逻辑**：
1. 根据不同领域（如金融产品分析、金融情绪识别等）构建不同的`system_content`
2. 将角色描述部分从原始prompt移至`system_content`
3. 在`system_content`中添加要求将答案放在`\boxed{}`格式中
4. 将问题和选项部分构建为`user_content`
5. 确保`assistant.content`中的答案使用`\boxed{}`包裹

**示例**：
原始数据：
```csv
id,question,A,B,C,answer
0,我的投资回报率一直很高。,中性,积极,消极,B
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "你是一名专业的金融分析师，你能够识别出一段金融文本的多种情绪分类：积极、中性、消极。接下来你会得到一段金融文本，你需要判断该文本所表达的情绪是属于积极、中性、消极的哪一类别。你需要从A、B、C三个选项中选出一个作为识别的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C中一个。请将答案放在\\boxed{}格式中。"},
    {"role": "user", "content": "金融文本是：我的投资回报率一直很高。\n选项：\nA.中性\nB.积极\nC.消极"},
    {"role": "assistant", "content": "\\boxed{B}", "reasoning": "", "final_answer": "\\boxed{B}"}
  ]
}
```

#### 2.1.11. fin-r1: Ant_Finance_SUFE

**任务描述**：金融领域专业考试题目，主要包含会计学、金融学等专业知识的选择题。

**原始格式**：CSV文件，包含字段：`id`，`question`，`A`，`B`，`C`，`D`，`answer`，`explanation`（仅dev数据集）
- `id`：问题ID
- `question`：问题内容
- `A`到`D`：选项内容
- `answer`：正确答案选项字母
- `explanation`：解释（仅dev数据集包含）

**标准化处理**：
- `system.content`：构建统一格式的系统提示，包含领域描述和要求将答案放在`\boxed{}`格式中
- `user.content`：构建统一格式的问题内容，包含问题和选项
- `assistant.content`：
  - 如果有解释字段（dev数据集）：`"{explanation} 因此，最终答案是：\boxed{{answer}}"`
  - 如果没有解释字段（val数据集）：直接使用`\boxed{}`包裹的正确答案选项字母
- `assistant.reasoning`：默认为空字符串
- `assistant.final_answer`：使用`\boxed{}`包裹的正确答案选项字母

**处理逻辑**：
1. 分别处理dev和val数据集，dev数据集包含explanation字段，val数据集不包含
2. 将"以下是中国关于{domain}考试的单项选择题，请选出其中的正确答案。"作为`system_content`
3. 在`system_content`中添加要求将答案放在`\boxed{}`格式中
4. 将问题和选项构建为`user_content`
5. 对于dev数据集，使用explanation构建带解释的回答
6. 确保所有回答中的答案使用`\boxed{}`包裹

**示例（dev数据集，包含解释）**：
原始数据：
```csv
id,question,A,B,C,D,answer,explanation
0,甲公司2×14年12月20日与乙公司签订商品销售合同。合同约定：甲公司需承担在商品运抵乙公司前灭失、毁损、价值变动等风险，于2×15年5月20日前将合同标的商品运抵乙公司并经验收。2×14年12月30日，甲公司根据合同向乙公司开具了增值税专用发票并于当日确认了商品销售收入。甲公司该项合同中所售商品为库存W商品，W商品于2×15年5月10日发出并于5月15日运抵乙公司验收合格。除考虑与会计准则规定的收入确认条件的符合性以外，对于甲公司2×14年W商品销售收入确认的恰当性判断，还应考虑可能违背的会计基本假设是____。,会计主体,会计分期,持续经营,短期经营,B,1. 由于题目中明确提到在商品运抵乙公司前灭失、毁损、价值变动等风险由甲公司承担，即相关商品的控制权并未转移，应在2×15年确认收入。甲公司在2×14年确认收入，因此违背了会计分期假设。
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "以下是中国关于会计学考试的单项选择题，请选出其中的正确答案。请将答案放在\\boxed{}格式中。"},
    {"role": "user", "content": "甲公司2×14年12月20日与乙公司签订商品销售合同。合同约定：甲公司需承担在商品运抵乙公司前灭失、毁损、价值变动等风险，于2×15年5月20日前将合同标的商品运抵乙公司并经验收。2×14年12月30日，甲公司根据合同向乙公司开具了增值税专用发票并于当日确认了商品销售收入。甲公司该项合同中所售商品为库存W商品，W商品于2×15年5月10日发出并于5月15日运抵乙公司验收合格。除考虑与会计准则规定的收入确认条件的符合性以外，对于甲公司2×14年W商品销售收入确认的恰当性判断，还应考虑可能违背的会计基本假设是____。\nA.会计主体\nB.会计分期\nC.持续经营\nD.短期经营"},
    {"role": "assistant", "content": "1. 由于题目中明确提到在商品运抵乙公司前灭失、毁损、价值变动等风险由甲公司承担，即相关商品的控制权并未转移，应在2×15年确认收入。甲公司在2×14年确认收入，因此违背了会计分期假设。 因此，最终答案是：\\boxed{B}", "reasoning": "", "final_answer": "\\boxed{B}"}
  ]
}
```

**示例（val数据集，不包含解释）**：
原始数据：
```csv
id,question,A,B,C,D,answer
0,甲公司在非同一控制下企业合并中取得10台生产设备，合并日以公允价值计量这些生产设备。甲公司可以进入X市场或Y市场出售这些生产设备，合并日相同生产设备每台交易价格分别为180万元和160万元。如果甲公司在X市场出售这些合并中取得的生产设备，需要支付相关交易费用90万元，将这些生产设备运到X市场需要支付运费50万元。如果甲公司在Y市场出售这些合并中取得的生产设备，需要支付相关交易费用60万元，将这些生产设备运到Y市场需要支付运费30万元。假定上述生产设备不存在主要市场，不考虑增值税及其他因素，甲公司上述生产设备的公允价值总额是____。,1640万元,1750万元,1730万元,1740万元,B
```
标准化后：
```json
{
  "id": "uuid4",
  "conversations": [
    {"role": "system", "content": "以下是中国关于会计学考试的单项选择题，请选出其中的正确答案。请将答案放在\\boxed{}格式中。"},
    {"role": "user", "content": "甲公司在非同一控制下企业合并中取得10台生产设备，合并日以公允价值计量这些生产设备。甲公司可以进入X市场或Y市场出售这些生产设备，合并日相同生产设备每台交易价格分别为180万元和160万元。如果甲公司在X市场出售这些合并中取得的生产设备，需要支付相关交易费用90万元，将这些生产设备运到X市场需要支付运费50万元。如果甲公司在Y市场出售这些合并中取得的生产设备，需要支付相关交易费用60万元，将这些生产设备运到Y市场需要支付运费30万元。假定上述生产设备不存在主要市场，不考虑增值税及其他因素，甲公司上述生产设备的公允价值总额是____。\nA.1640万元\nB.1750万元\nC.1730万元\nD.1740万元"},
    {"role": "assistant", "content": "\\boxed{B}", "reasoning": "", "final_answer": "\\boxed{B}"}
  ]
}
```

## 三、数据合成

### 3.1 方案设计

#### 3.1.1 pipeline

转化：'LLM' -> MCQ数据 -> 开放式问题 + 答案

蒸馏：'LLM' -> QA数据 -> 推理过程 + 推理答案

验证：'LLM' -> 答案 + 推理答案 -> 答案正确性标记

评价：'LLM' -> 推理过程 -> 推理质量标记

### 3.1.1 抽样

|数据集|路径|参考量|抽样量|总量|抽样率|数据占比|
| --- | --- | ---: | ---: | ---: | ---: | ---: |
|ConvFinQA|fin-r1/fingpt-convfinqa|7629|8000|12595|63.52%|13.09%|
|Finance-Instruct|data/processed/fin-r1/Finance-Instruct-500k|11300|12000|518185|2.32%|19.64%|
|FinCUGE|fin-r1/FinCUGE-Instruction|2000|2000|137852|1.45%|3.27%|
|FinQA|fin-r1/finqa|2948|3000|8281|36.23%|4.91%|
|TFNS|fin-r1/twitter-financial-news-sentiment|2451|2500|11931|20.95%|4.09%|
|FinanceIQ|fin-r1/FinanceIQ|2596|2600|7173|36.25%|4.26%|
|FinanceQT|fin-r1/Quant-Trading-Instruct|152|386|386|100.00%|0.63%|
|Ant_Finance|fin-r1/ant_finance|1548|800|8444|9.47%|1.31%|
|Ant_Finance_SUFE|fin-r1/ant_finance_SUFE|1548|800|4581|17.46%|1.31%|
|FinCorpus|fin-r1/FinCorpus|29288|30000|489916|6.12%|49.18%|
|总和||60091|61086|1199344|5.09%|100.00%|

转化 x1
蒸馏 x1
验证 x1
评价 x1

### 3.2 转化

<center>表3-1 多选题转开放式问题模板</center>

| English | Chinese |
| --- | --- |
| I will provide you with a multiple-choice question, and your task is to rewrite it as an open-ended question and provide a standard answer. Requirements: | 我将向你提供一个多选题，你的任务是将其重写成为一个开放式问题，并提供标准答案。要求如下： |
| 1. **The question must be specific**, targeting the key points tested in the original multiple-choice question. Ensure the question is open-ended, meaning no options are provided, but it must have a clear standard answer. | 1. **问题必须具体**，针对原多选题中测试的要点。确保问题是开放式的，即不提供选项，但必须有明确的标准答案。 |
| 2. **Based on the correct answer to the original question**, provide a concise standard answer. The answer should allow for exact matching to determine if a model's response is correct. | 2. **根据原问题的正确答案**，提供一个简洁的标准答案。答案应当允许精确匹配以确定模型的回答是否正确。 |
| ### Multiple-choice Question<br>{question}<br>{options}<br>### Correct Answer<br>{correct_answer} | ### 多选题问题<br>{question}<br>{options}<br>### 正确答案<br>{correct_answer} |
| ## Strictly output in the following JSON format:<br>{<br>    "question": "", # Open-ended Question<br>    "answer": ""    # Standard Answer<br>} | ## 严格按照以下JSON格式输出：<br>{<br>    "question": "", # 开放式问题<br>    "answer": ""    # 标准答案<br>} |

### 3.3 蒸馏

<center>表3-2 数据蒸馏模板</center>

| English | Chinese |
| --- | --- |
| Task description: {task_desc}<br>Task input: {task_input}<br>Task instruction: {task_instruction}<br>Please analyze the given input based on the above task description and instruction, and generate the output that meets the requirements. | 任务描述: {task_desc}<br>任务输入: {task_input}<br>任务指令: {task_instruction}<br>请根据上述任务描述和指令分析给定的输入，并生成符合要求的输出。 |
| Notes:<br>1. First, think through the problem step by step. Your reasoning will be captured separately.<br>2. After your reasoning, provide a clear and concise final answer.<br>3. Please enclose the final answer in the format \boxed{{}}, for regular extraction. For example: \boxed{{Final answer}}<br>4. Add "\n" at the beginning of each output before generating the data. | 注意事项：<br>1. 首先，逐步思考问题。你的推理过程将被单独捕获。<br>2. 在推理之后，提供一个清晰简洁的最终答案。<br>3. 请使用\boxed{{}}格式包裹最终答案，以便正则提取。例如：\boxed{{最终答案}}<br>4. 在生成数据之前，在每个输出的开头添加"\n"。 |

### 3.4 验证

<center>表3-3 答案验证模板</center>

| English | Chinese |
| --- | --- |
| You are a scoring assistant for financial questions. I will provide you with a <ground truth> and a <model answer>. Please determine whether the <model answer> has the same meaning as the <ground truth> according to the following rules. If they are consistent, output 1, otherwise output 0. | 你是金融问题的评分助手。我将为你提供一个<标准答案>和一个<模型答案>。请根据以下规则判断<模型答案>是否与<标准答案>具有相同的含义。如果它们一致，输出1，否则输出0。 |
| <ground truth><br>{Truth}<br></ground truth><br><br><model answer><br>{Answer}<br></model answer> | <标准答案><br>{Truth}<br></标准答案><br><br><模型答案><br>{Answer}<br></模型答案> |
| ### Rules:<br>1.If the <ground truth> is a numerical value, and the format of the <model answer> is different from that of the <ground truth>, but the numerical values are the same, then it is considered that the meanings are consistent. For example, if the <ground truth> is 0.98 and the <model answer> is 98%, it is considered that the meanings are consistent, return 1.<br>2.If the <ground truth> is a numerical value, and the finalresult of the <model answer> is consistent with the <ground truth> after rounding, then it is considered that the meanings are consistent. For example, if the <ground truth> is 2 and the <model answer> is 1.98, it is considered that the meanings are consistent, return 1. | ### 规则：<br>1.如果<标准答案>是数值，且<模型答案>的格式与<标准答案>不同，但数值相同，则认为含义一致。例如，如果<标准答案>是0.98，而<模型答案>是98%，则认为含义一致，返回1。<br>2.如果<标准答案>是数值，且<模型答案>的最终结果在四舍五入后与<标准答案>一致，则认为含义一致。例如，如果<标准答案>是2，而<模型答案>是1.98，则认为含义一致，返回1。 |
| ### Output Format:<br>Make the judgment according to the above rules, and finally put the judgment result 1 or 0 in \\boxed{{}}, for example, \\boxed{{1}} or \\boxed{{0}} | ### 输出格式：<br>根据上述规则进行判断，最后将判断结果1或0放在\\boxed{{}}中，例如，\\boxed{{1}}或\\boxed{{0}} |

### 3.5 评价

<center>表3-4 推理质量评价模板</center>

| English | Chinese |
| --- | --- |
| Please evaluate the quality of the reasoning process based on the following criteria: | 请根据以下标准评估推理过程的质量： |
| 1. **Internal Consistency**: Check if the steps in the reasoning process are consistent and logically lead to the correct answer.<br>2. **Term Overlap**: Check the overlap between terms used in the reasoning process and the standard answer. The higher the overlap, the better.<br>3. **Number of Reasoning Steps**: Ensure the reasoning process includes enough steps (at least 3 steps).<br>4. **Logical Consistency**: Ensure that the steps in the reasoning process align with the standard answer in terms of logic, with no major errors or omissions.<br>5. **Content Diversity**: Check if the reasoning process contains repetitive steps.<br>6. **Task Domain Relevance**: Ensure the reasoning is relevant to the task domain (task domain: {task_domain}). Higher relevance to the task domain should result in a higher score.<br>7. **Task Instruction Relevance**: Check if the reasoning closely follows the task instructions. The more closely the reasoning matches the task instructions, the higher the score. Task instructions: {task_instruction} | 1. **内部一致性**：检查推理过程中的步骤是否一致，并在逻辑上导向正确答案。<br>2. **术语重叠**：检查推理过程中使用的术语与标准答案之间的重叠。重叠越高越好。<br>3. **推理步骤数量**：确保推理过程包含足够的步骤（至少3个步骤）。<br>4. **逻辑一致性**：确保推理过程中的步骤在逻辑上与标准答案一致，没有重大错误或遗漏。<br>5. **内容多样性**：检查推理过程是否包含重复的步骤。<br>6. **任务领域相关性**：确保推理与任务领域相关（任务领域：{task_domain}）。与任务领域的相关性越高，得分应越高。<br>7. **任务指令相关性**：检查推理是否紧密遵循任务指令。推理与任务指令匹配得越紧密，得分越高。任务指令：{task_instruction} |
| Below is the reasoning process and the standard answer:<br>Reasoning: {reasoning}<br>Standard Answer: {gold_answer} | 以下是推理过程和标准答案：<br>推理：{reasoning}<br>标准答案：{gold_answer} |
| Please provide a score based on the following criteria:<br>- The seven indicators mentioned above are scored 1 point each. If the total score is 7, it is considered a high-quality reasoning trajectory, and the output is 1. Otherwise, the output is 0.<br>- Please wrap the score in \\boxed{{}} format for easy extraction using regex. For example: \\boxed{{final_score}} | 请根据以下标准提供评分：<br>- 上述七个指标每个计1分。如果总分为7分，则被视为高质量的推理轨迹，输出为1。否则，输出为0。<br>- 请使用\\boxed{{}}格式包裹分数，以便使用正则表达式轻松提取。例如：\\boxed{{final_score}} |
