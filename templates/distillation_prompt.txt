### Task description

{task_desc}

### Task instruction

{task_instruction}

### Task input

{task_input}

Please analyze the given input based on the above task description and instruction, and generate the output that meets the requirements.

Notes:
1. First, think through the problem step by step. Your reasoning will be captured separately.
2. After your reasoning, provide a clear and concise final answer.
3. Please enclose the final answer in the format \\boxed{{}}, for regular extraction. For example: \\boxed{{Final answer}}
4. Add "\n" at the beginning of each output before generating the data.
