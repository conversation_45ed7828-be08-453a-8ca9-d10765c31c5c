#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""大规模LLM API调用的并发处理引擎

主要功能:
- 多线程并发处理大规模API请求
- 自动处理限流、重试和错误恢复
- 支持实时监控和状态追踪
- 异步结果写入和文件管理

使用示例:
    def construct_prompt(data):
        return "指令", f"处理数据: {data['content']}"

    def process_response(task, response):
        return {"id": task["id"], "result": response}

    config = {
        "API_KEY": "your-api-key",
        "API_URL": "https://api.example.com/v1/chat",
        "MODEL": "gpt-3.5-turbo"
    }

    run_synthesis(
        input_dir="data/input",
        output_dir="data/output",
        construct_prompt_func=construct_prompt,
        process_response_func=process_response,
        config=config
    )

注意事项:
- 输入文件必须是.jsonl格式
- 需要正确配置API密钥和URL
- 建议根据API限制调整并发数和重试参数
- 支持优雅中断(Ctrl+C)，会保证正在处理的任务完成
"""
import json
import os
import time
import threading
from queue import Queue
from typing import Dict, Any, Callable
import requests
import logging
from datetime import datetime

class SynthesisEngine:
    def __init__(self,
                 config: Dict[str, Any],
                 construct_prompt_func: Callable,
                 process_response_func: Callable):
        """初始化合成引擎

        Args:
            config: 配置参数字典
            construct_prompt_func: 构造提示词的函数
            process_response_func: 处理响应的函数
        """
        # 存储业务回调函数
        self.construct_prompt = construct_prompt_func
        self.process_response = process_response_func

        # 配置项提取
        self.INPUT_DIR = config.get("INPUT_DIR", "data/input")
        self.OUTPUT_DIR = config.get("OUTPUT_DIR", "data/output")
        self.API_URL = config.get("API_URL", "")
        self.API_KEY = config.get("API_KEY", "")
        self.MAX_RETRIES = config.get("MAX_RETRIES", 3)
        self.NUM_PROCESSORS = config.get("NUM_PROCESSORS", 4)
        self.RATE_LIMIT_WAIT = config.get("RATE_LIMIT_WAIT", 10)
        self.MODEL = config.get("MODEL", "")
        self.MAX_QUEUE_SIZE = config.get("MAX_QUEUE_SIZE", 100)
        self.API_PARAMS = config.get("API_PARAMS", {})

        # 数据结构
        self.task_queue = Queue(maxsize=self.MAX_QUEUE_SIZE)
        self.result_queue = Queue()
        self.system_state = threading.Event()
        self.system_state.set()  # 默认为运行状态
        self.all_producers_done = False

        # 统计数据
        self.stats_lock = threading.Lock()
        self.stats = {
            "processed": 0,
            "success": 0,
            "failed": 0,
            "retries": 0,
            "last_error": None
        }

        # 设置日志
        self.logger = self.setup_logging()

    def setup_logging(self):
        """配置日志"""
        os.makedirs("logs", exist_ok=True)
        log_file = f"logs/synthesis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)

    def pause_system(self, reason, duration=None):
        """暂停系统运行一段时间"""
        if duration is None:
            duration = self.RATE_LIMIT_WAIT

        if self.system_state.is_set():  # 只有在系统运行时才需要暂停
            self.system_state.clear()  # 暂停系统
            self.logger.info(f"系统暂停: {reason}，将在{duration}秒后恢复")

            # 使用定时器在指定时间后恢复系统
            def resume():
                if not self.system_state.is_set():  # 确认系统仍处于暂停状态
                    self.system_state.set()
                    self.logger.info("系统已恢复运行")

            threading.Timer(duration, resume).start()

    class TaskProducer(threading.Thread):
        """从输入文件读取数据并添加到任务队列"""

        def __init__(self, engine):
            super().__init__()
            self.engine = engine

        def run(self):
            engine = self.engine
            engine.logger.info("任务生产者启动")
            try:
                for filename in os.listdir(engine.INPUT_DIR):
                    if not filename.endswith(".jsonl"):
                        continue

                    input_path = os.path.join(engine.INPUT_DIR, filename)
                    output_path = os.path.join(engine.OUTPUT_DIR, filename)

                    # 创建输出目录
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)

                    with open(input_path, "r", encoding="utf-8") as f_in:
                        for line in f_in:
                            # 系统暂停时等待
                            if not engine.system_state.is_set():
                                engine.system_state.wait()  # 等待系统恢复

                            data = json.loads(line.strip())
                            instruction, prompt = engine.construct_prompt(data)

                            # 创建任务
                            task = {
                                "id": data["id"],
                                "instruction": instruction,
                                "prompt": prompt,
                                "retries": 0,
                                "output_file": output_path,
                                "raw_data": data
                            }

                            # 添加到队列 (会在队列满时自动阻塞)
                            engine.task_queue.put(task)

                engine.logger.info("所有任务已添加到队列")
            except Exception as e:
                engine.logger.error(f"任务生产者出错: {str(e)}")
            finally:
                # 添加结束标记
                for _ in range(engine.NUM_PROCESSORS):
                    engine.task_queue.put(None)
                engine.logger.info("任务生产者已完成")

    class TaskProcessor(threading.Thread):
        """从任务队列获取任务并处理"""

        def __init__(self, engine, worker_id):
            super().__init__()
            self.engine = engine
            self.worker_id = worker_id

        def run(self):
            engine = self.engine
            engine.logger.info(f"处理者 #{self.worker_id} 启动")

            while True:
                try:
                    # 系统暂停时等待
                    if not engine.system_state.is_set():
                        engine.system_state.wait()

                    # 获取任务 (timeout避免一直阻塞)
                    task = engine.task_queue.get(timeout=1)

                    # 接收到结束信号
                    if task is None:
                        engine.logger.info(f"处理者 #{self.worker_id} 收到结束信号")
                        engine.task_queue.task_done()
                        break

                    # 处理任务
                    try:
                        result = self.process_task(task)

                        if result:
                            # 成功处理，添加到结果队列
                            engine.result_queue.put(result)

                            with engine.stats_lock:
                                engine.stats["success"] += 1
                                engine.stats["processed"] += 1

                    except Exception as e:
                        # 有任何错误就重试
                        self.handle_error(task, str(e))

                    # 标记任务完成
                    engine.task_queue.task_done()

                except Exception as e:
                    # 队列操作出错等情况
                    engine.logger.error(f"处理者 #{self.worker_id} 运行出错: {str(e)}")
                    time.sleep(1)  # 避免CPU空转

            engine.logger.info(f"处理者 #{self.worker_id} 已停止")

        def process_task(self, task):
            """处理单个任务"""
            engine = self.engine

            # 构建API请求参数
            api_params = engine.API_PARAMS.copy()
            api_params.update({
                "model": engine.MODEL,
                "messages": [{"role": "user", "content": task["prompt"]}],
                # 添加reasoning参数，获取模型的思考过程
                "reasoning": {
                    "effort": "high",  # 可以是 "high", "medium", "low"
                    "exclude": False   # 不排除reasoning tokens
                }
            })

            # 发送API请求
            response = requests.post(
                engine.API_URL,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {engine.API_KEY}"
                },
                json=api_params,
                timeout=120
            )

            # 检查状态码
            if response.status_code != 200:
                error_msg = f"API错误: {response.status_code}"

                # 处理限流错误
                if response.status_code in [429, 432, 433]:
                    error_msg = f"限流错误: {response.status_code}"
                    engine.pause_system(error_msg, engine.RATE_LIMIT_WAIT)

                # 处理认证错误
                if response.status_code == 401:
                    engine.logger.critical("认证失败，程序终止")
                    os._exit(1)

                raise Exception(error_msg)

            # 获取API响应结果
            result = response.json()
            llm_response = result["choices"][0]["message"]["content"]

            # 获取reasoning字段（如果存在）
            reasoning = ""
            if "reasoning" in result["choices"][0]["message"]:
                reasoning = result["choices"][0]["message"]["reasoning"]
                engine.logger.info(f"成功获取reasoning字段，长度: {len(reasoning)}")
            else:
                engine.logger.warning(f"API响应中没有reasoning字段")

            # 使用业务处理函数处理响应
            processed_result = engine.process_response(task, llm_response, reasoning)
            processed_result["output_file"] = task["output_file"]

            return processed_result

        def handle_error(self, task, error_msg):
            """处理任务错误"""
            engine = self.engine
            engine.logger.error(f"任务 {task['id']} 处理失败: {error_msg}")

            with engine.stats_lock:
                engine.stats["retries"] += 1
                engine.stats["last_error"] = error_msg

            # 处理其他错误的重试逻辑
            if task["retries"] < engine.MAX_RETRIES:
                task["retries"] += 1
                backoff_time = 5 * task["retries"]  # 简单线性回退

                engine.logger.info(f"任务 {task['id']} 将在 {backoff_time} 秒后重试 (第 {task['retries']} 次)")

                # 延迟重新入队
                def requeue():
                    engine.task_queue.put(task)
                    engine.logger.info(f"任务 {task['id']} 已重新入队")

                threading.Timer(backoff_time, requeue).start()
            else:
                engine.logger.warning(f"任务 {task['id']} 已达最大重试次数")
                with engine.stats_lock:
                    engine.stats["failed"] += 1
                    engine.stats["processed"] += 1

    class ResultWriter(threading.Thread):
        """从结果队列获取结果并写入文件"""

        def __init__(self, engine):
            super().__init__()
            self.engine = engine

        def run(self):
            engine = self.engine
            engine.logger.info("结果写入者启动")

            # 用于跟踪已处理的文件
            processed_files = {}

            while True:
                try:
                    # 获取结果 (阻塞等待，不使用timeout)
                    result = engine.result_queue.get()

                    # 接收到结束信号
                    if result is None:
                        engine.logger.info("结果写入者收到结束信号")
                        engine.result_queue.task_done()
                        break

                    # 写入结果
                    output_file = result["output_file"]

                    with open(output_file, "a", encoding="utf-8") as f_out:
                        # 删除输出文件路径再写入
                        result_copy = result.copy()
                        result_copy.pop("output_file", None)
                        f_out.write(json.dumps(result_copy, ensure_ascii=False) + "\n")

                    # 更新统计
                    if output_file not in processed_files:
                        processed_files[output_file] = 0
                    processed_files[output_file] += 1

                    # 标记结果处理完成
                    engine.result_queue.task_done()

                except Exception as e:
                    engine.logger.error(f"结果写入者出错: {str(e)}")
                    time.sleep(1)  # 发生真正错误时暂停一秒

            engine.logger.info(f"结果写入者已完成，共处理 {sum(processed_files.values())} 个结果")

    class Monitor(threading.Thread):
        """监控系统状态"""

        def __init__(self, engine):
            super().__init__()
            self.engine = engine

        def run(self):
            engine = self.engine
            engine.logger.info("监控线程启动")

            while True:
                try:
                    with engine.stats_lock:
                        status = (
                            f"状态：{'运行中' if engine.system_state.is_set() else '暂停中'} | "
                            f"任务队列：{engine.task_queue.qsize()} | "
                            f"结果队列：{engine.result_queue.qsize()} | "
                            f"已处理：{engine.stats['processed']} | "
                            f"成功：{engine.stats['success']} | "
                            f"失败：{engine.stats['failed']} | "
                            f"重试：{engine.stats['retries']}"
                        )

                        if engine.stats["last_error"]:
                            status += f" | 最新错误：{engine.stats['last_error']}"
                            engine.stats["last_error"] = None  # 清除错误

                    engine.logger.info(status)

                    # 检查是否所有任务都已完成
                    if engine.task_queue.empty() and engine.all_producers_done:
                        engine.logger.info("所有任务已处理完毕，准备退出")

                        # 向结果写入者发送结束信号
                        engine.result_queue.put(None)
                        break

                    time.sleep(5)  # 每5秒更新一次

                except Exception as e:
                    engine.logger.error(f"监控线程出错: {str(e)}")
                    time.sleep(5)

            engine.logger.info("监控线程已停止")

    def run(self):
        """启动处理引擎"""
        self.logger.info("系统启动")

        # 创建输出目录
        os.makedirs(self.OUTPUT_DIR, exist_ok=True)

        # 创建线程
        producer = self.TaskProducer(self)
        processors = [self.TaskProcessor(self, i) for i in range(self.NUM_PROCESSORS)]
        writer = self.ResultWriter(self)
        monitor = self.Monitor(self)

        # 设置为守护线程
        producer.daemon = True
        for p in processors:
            p.daemon = True
        writer.daemon = True
        monitor.daemon = True

        # 启动线程
        producer.start()
        for p in processors:
            p.start()
        writer.start()
        monitor.start()

        try:
            # 等待生产者完成
            producer.join()
            self.all_producers_done = True
            self.logger.info("任务生产者已完成")

            # 等待处理队列清空
            self.task_queue.join()
            self.logger.info("所有任务已处理完毕")

            # 等待结果队列清空
            self.result_queue.join()
            self.logger.info("所有结果已写入完毕")

            # 等待监控线程结束
            monitor.join()

        except KeyboardInterrupt:
            self.logger.info("收到中断信号，准备安全退出")
            self.system_state.clear()  # 暂停系统

            # 清空所有队列
            try:
                while not self.task_queue.empty():
                    self.task_queue.get_nowait()
                    self.task_queue.task_done()

                while not self.result_queue.empty():
                    self.result_queue.get_nowait()
                    self.result_queue.task_done()
            except Exception as e:
                self.logger.error(f"清空队列时出错: {str(e)}")

        finally:
            self.logger.info("系统已安全退出")

# 便捷启动函数
def run_synthesis(input_dir,
                  output_dir,
                  construct_prompt_func,
                  process_response_func,
                  config=None):
    """
    便捷启动合成引擎

    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        construct_prompt_func: 构造提示词的函数
        process_response_func: 处理响应的函数
        config: 其他配置参数
    """
    if config is None:
        config = {}

    # 合并配置
    full_config = config.copy()
    full_config["INPUT_DIR"] = input_dir
    full_config["OUTPUT_DIR"] = output_dir

    # 创建并启动引擎
    engine = SynthesisEngine(
        config=full_config,
        construct_prompt_func=construct_prompt_func,
        process_response_func=process_response_func
    )

    engine.run()